import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";
import { runUserMigration } from "./migrate-users";
import { validateTelegramWebAppData } from "./telegram-auth";

admin.initializeApp();
const db = admin.firestore();

export const createUserRecord = functions.auth.user().onCreate(async (user) => {
  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    createdAt:
      admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    console.log(`User record created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user record:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating user record."
    );
  }
});

export const getUserProfile = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required to get user profile."
    );
  }

  try {
    const userDoc = await db.collection("users").doc(context.auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        "not-found",
        "User profile not found."
      );
    }

    return userDoc.data();
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user profile."
    );
  }
});

export const tonTransactionMonitor = functions.pubsub
  .schedule("* * * * *") // Every 1 minute
  .timeZone("UTC")
  .onRun(async () => {
    try {
      console.log(
        "TON transaction monitor triggered at:",
        new Date().toISOString()
      );
      await monitorTonTransactions();
      console.log("TON transaction monitor completed successfully");
    } catch (error) {
      console.error("TON transaction monitor failed:", error);
    }
  });

// One-time migration function to add raw_ton_wallet_address to existing users
export const migrateUserTonAddresses = functions.https.onCall(
  async (_data, context) => {
    // Only allow admin users to run migration
    if (!context.auth || !context.auth.token.admin) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admin users can run migrations"
      );
    }

    try {
      await runUserMigration();
      return { success: true, message: "Migration completed successfully" };
    } catch (error) {
      console.error("Migration failed:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Migration failed: " + (error as Error).message
      );
    }
  }
);

// Telegram authentication function
export const authenticateWithTelegram = functions.https.onCall(async (data) => {
  const { initData } = data;

  if (!initData) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "initData is required"
    );
  }

  // Get bot token from environment variables
  const botToken = functions.config().telegram?.bot_token;
  if (!botToken) {
    throw new functions.https.HttpsError(
      "failed-precondition",
      "Telegram bot token not configured"
    );
  }

  try {
    // Validate Telegram data
    const validation = validateTelegramWebAppData(initData, botToken);

    if (!validation.isValid || !validation.user) {
      throw new functions.https.HttpsError(
        "permission-denied",
        validation.error || "Invalid Telegram data"
      );
    }

    const telegramUser = validation.user;
    const telegramId = telegramUser.id.toString();

    // Check if user already exists
    const existingUserQuery = await db
      .collection("users")
      .where("tg_id", "==", telegramId)
      .limit(1)
      .get();

    let userId: string;
    let userRecord: UserEntity;

    if (!existingUserQuery.empty) {
      // User exists, get their data
      const existingUserDoc = existingUserQuery.docs[0];
      userId = existingUserDoc.id;
      userRecord = existingUserDoc.data() as UserEntity;
    } else {
      // Create new user
      const newUserRef = db.collection("users").doc();
      userId = newUserRef.id;

      userRecord = {
        id: userId,
        email: null,
        displayName:
          telegramUser.first_name +
          (telegramUser.last_name ? ` ${telegramUser.last_name}` : ""),
        photoURL: telegramUser.photo_url || null,
        role: "user",
        tg_id: telegramId,
        createdAt:
          admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
      };

      await newUserRef.set(userRecord);
      console.log(
        `New Telegram user created: ${userId} (TG ID: ${telegramId})`
      );
    }

    // Create custom token for Firebase Auth
    const customToken = await admin.auth().createCustomToken(userId, {
      tg_id: telegramId,
      provider: "telegram",
    });

    return {
      customToken,
      user: userRecord,
    };
  } catch (error) {
    console.error("Telegram authentication error:", error);

    if (error instanceof functions.https.HttpsError) {
      throw error;
    }

    throw new functions.https.HttpsError("internal", "Authentication failed");
  }
});

export {
  getBalance,
  checkPurchaseEligibility,
  lockOrderFunds,
  unlockOrderFunds,
  completeOrder,
} from "./balance-functions";
