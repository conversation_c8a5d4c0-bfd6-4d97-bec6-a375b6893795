import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";
import { runUserMigration } from "./migrate-users";
import { validateTelegramWebAppData } from "./telegram-auth";

// Initialize Firebase Admin with proper configuration for custom tokens
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    // Explicitly set the project ID to ensure proper token signing
    projectId: process.env.GCLOUD_PROJECT || process.env.FIREBASE_PROJECT_ID,
  });
}

const db = admin.firestore();

export const createUserRecord = functions.auth.user().onCreate(async (user) => {
  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    createdAt:
      admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    console.log(`User record created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user record:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating user record."
    );
  }
});

export const getUserProfile = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required to get user profile."
    );
  }

  try {
    const userDoc = await db.collection("users").doc(context.auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        "not-found",
        "User profile not found."
      );
    }

    return userDoc.data();
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user profile."
    );
  }
});

export const tonTransactionMonitor = functions.pubsub
  .schedule("* * * * *") // Every 1 minute
  .timeZone("UTC")
  .onRun(async () => {
    try {
      console.log(
        "TON transaction monitor triggered at:",
        new Date().toISOString()
      );
      await monitorTonTransactions();
      console.log("TON transaction monitor completed successfully");
    } catch (error) {
      console.error("TON transaction monitor failed:", error);
    }
  });

// One-time migration function to add raw_ton_wallet_address to existing users
export const migrateUserTonAddresses = functions.https.onCall(
  async (_data, context) => {
    // Only allow admin users to run migration
    if (!context.auth || !context.auth.token.admin) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admin users can run migrations"
      );
    }

    try {
      await runUserMigration();
      return { success: true, message: "Migration completed successfully" };
    } catch (error) {
      console.error("Migration failed:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Migration failed: " + (error as Error).message
      );
    }
  }
);

// TODO read from env

// Telegram authentication function
export const authenticateWithTelegram = functions.https.onCall(async (data) => {
  const MIKE_BOT_TOKEN = "7870296231:AAH4uVlMKmpRreydCXCe0AysQ9Jvyf8BEmY";
  const isDevelopment = process.env.NODE_ENV !== "production";

  const { initData } = data;

  if (!initData) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "initData is required"
    );
  }

  console.log("[authenticateWithTelegram] Received initData:", initData);
  console.log("[authenticateWithTelegram] Development mode:", isDevelopment);

  // Get bot token from environment variables
  const botToken = MIKE_BOT_TOKEN;
  if (!botToken) {
    throw new functions.https.HttpsError(
      "failed-precondition",
      "Telegram bot token not configured"
    );
  }

  try {
    let telegramUser: any;
    let telegramId: string;

    // In development mode, allow mock data for testing
    if (isDevelopment && initData.includes("mock_hash_for_development")) {
      console.log(
        "[authenticateWithTelegram] Using development mode with mock data"
      );

      const urlParams = new URLSearchParams(initData);
      const userParam = urlParams.get("user");

      if (!userParam) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "User data is missing in mock initData"
        );
      }

      telegramUser = JSON.parse(userParam);
      telegramId = telegramUser.id.toString();

      console.log("[authenticateWithTelegram] Mock user:", telegramUser);
    } else {
      // Production mode: validate Telegram data properly
      console.log("[authenticateWithTelegram] Validating Telegram data...");
      const validation = validateTelegramWebAppData(initData, botToken);

      console.log("[authenticateWithTelegram] Validation result:", {
        isValid: validation.isValid,
        error: validation.error,
        hasUser: !!validation.user,
      });

      if (!validation.isValid || !validation.user) {
        throw new functions.https.HttpsError(
          "permission-denied",
          validation.error || "Invalid Telegram data"
        );
      }

      telegramUser = validation.user;
      telegramId = telegramUser.id.toString();
    }

    console.log("[authenticateWithTelegram] Final user data:", {
      telegramId,
      firstName: telegramUser.first_name,
      username: telegramUser.username,
    });

    // Check if user already exists
    const existingUserQuery = await db
      .collection("users")
      .where("tg_id", "==", telegramId)
      .limit(1)
      .get();

    let userId: string;
    let userRecord: UserEntity;

    if (!existingUserQuery.empty) {
      // User exists, get their data
      const existingUserDoc = existingUserQuery.docs[0];
      userId = existingUserDoc.id;
      userRecord = existingUserDoc.data() as UserEntity;
    } else {
      // Create new user
      const newUserRef = db.collection("users").doc();
      userId = newUserRef.id;

      userRecord = {
        id: userId,
        email: null,
        displayName:
          telegramUser.first_name +
          (telegramUser.last_name ? ` ${telegramUser.last_name}` : ""),
        photoURL: telegramUser.photo_url ?? null,
        role: "user",
        tg_id: telegramId,
        createdAt:
          admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
      };

      await newUserRef.set(userRecord);
      console.log(
        `New Telegram user created: ${userId} (TG ID: ${telegramId})`
      );
    }

    // Create custom token for Firebase Auth
    console.log(
      "[authenticateWithTelegram] Creating custom token for user:",
      userId
    );

    try {
      const customToken = await admin.auth().createCustomToken(userId, {
        tg_id: telegramId,
        provider: "telegram",
      });

      console.log(
        "[authenticateWithTelegram] Custom token created successfully"
      );

      return {
        customToken,
        user: userRecord,
      };
    } catch (tokenError) {
      console.error(
        "[authenticateWithTelegram] Error creating custom token:",
        tokenError
      );

      // If custom token creation fails, we can still return user data
      // The client can handle authentication differently
      throw new functions.https.HttpsError(
        "internal",
        `Failed to create authentication token: ${
          tokenError instanceof Error ? tokenError.message : "Unknown error"
        }`
      );
    }
  } catch (error) {
    console.error("Telegram authentication error:", error);

    if (error instanceof functions.https.HttpsError) {
      throw error;
    }

    throw new functions.https.HttpsError("internal", "Authentication failed");
  }
});

export {
  getBalance,
  checkPurchaseEligibility,
  lockOrderFunds,
  unlockOrderFunds,
  completeOrder,
} from "./balance-functions";
