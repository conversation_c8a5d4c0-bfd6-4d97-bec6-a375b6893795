"use client";

import { useEffect, useState } from "react";
import { signInWithCustomToken } from "firebase/auth";
import { httpsCallable } from "firebase/functions";

import { firebaseAuth, firebaseFunctions } from "@/root-context";
import {
  getTelegramWebAppData,
  initTelegramWebApp,
  isTelegramWebApp,
} from "@/utils/telegram-auth";

interface TelegramAuthProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const TelegramAuth = ({ onSuccess, onError }: TelegramAuthProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isTelegramApp, setIsTelegramApp] = useState(false);

  useEffect(() => {
    const checkTelegramApp = () => {
      console.log("[TelegramAuth] Checking Telegram environment...");
      console.log("[TelegramAuth] window.Telegram:", window.Telegram);
      console.log(
        "[TelegramAuth] window.Telegram?.WebApp:",
        window.Telegram?.WebApp
      );

      const isInTelegram = isTelegramWebApp();
      console.log("[TelegramAuth] isTelegramWebApp result:", isInTelegram);
      setIsTelegramApp(isInTelegram);

      if (isInTelegram) {
        console.log("[TelegramAuth] Initializing Telegram Web App...");
        const initResult = initTelegramWebApp();
        console.log("[TelegramAuth] Init result:", initResult);

        // Log the initData
        console.log(
          "[TelegramAuth] initData:",
          window.Telegram?.WebApp?.initData
        );
        console.log(
          "[TelegramAuth] initDataUnsafe:",
          window.Telegram?.WebApp?.initDataUnsafe
        );
      }
    };

    checkTelegramApp();
    const timer1 = setTimeout(checkTelegramApp, 500);
    const timer2 = setTimeout(checkTelegramApp, 1000);
    const timer3 = setTimeout(checkTelegramApp, 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);

  const handleTelegramAuth = async () => {
    if (!isTelegramApp) {
      onError?.(
        "This app must be opened from Telegram Web App. Please open it from a Telegram bot or mini app."
      );
      return;
    }

    setIsLoading(true);

    try {
      console.log("[TelegramAuth] Starting authentication...");
      console.log("[TelegramAuth] Current window.Telegram:", window.Telegram);
      console.log(
        "[TelegramAuth] Current initData:",
        window.Telegram?.WebApp?.initData
      );

      const telegramData = getTelegramWebAppData();
      console.log("[TelegramAuth] Retrieved telegramData:", telegramData);

      if (!telegramData) {
        console.error("[TelegramAuth] No telegram data available");
        throw new Error(
          "Unable to retrieve Telegram user data. Please ensure you're opening this from a Telegram Web App."
        );
      }

      // Validate that we have the required initData
      if (!window.Telegram?.WebApp?.initData) {
        console.error("[TelegramAuth] No initData available");
        throw new Error(
          "Telegram Web App initialization data is missing. Please try refreshing the app."
        );
      }

      console.log("[TelegramAuth] Calling Firebase function...");
      const authenticateWithTelegram = httpsCallable(
        firebaseFunctions,
        "authenticateWithTelegram"
      );

      const initDataToSend = window.Telegram?.WebApp?.initData || "";
      console.log("[TelegramAuth] Sending initData:", initDataToSend);

      const result = await authenticateWithTelegram({
        initData: initDataToSend,
      });

      console.log("[TelegramAuth] Firebase function result:", result);

      const { customToken } = result.data as { customToken: string };

      await signInWithCustomToken(firebaseAuth, customToken);

      onSuccess?.();
    } catch (error) {
      console.error("Telegram authentication error:", error);

      let errorMessage = "Authentication failed";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === "object" &&
        error !== null &&
        "message" in error
      ) {
        errorMessage = String(error.message);
      }

      // Provide more specific error messages
      if (errorMessage.includes("Failed to get Telegram data")) {
        errorMessage =
          "Unable to access Telegram user data. Please ensure you're opening this app from within Telegram.";
      } else if (errorMessage.includes("initData")) {
        errorMessage =
          "Telegram Web App data is missing. Please try refreshing the app or reopening from Telegram.";
      } else if (
        errorMessage.includes("internal") ||
        errorMessage.includes("INTERNAL")
      ) {
        errorMessage =
          "Server configuration error. The Telegram authentication service needs to be set up. Please contact the administrator.";
      } else if (
        errorMessage.includes("UNAUTHENTICATED") ||
        errorMessage.includes("permission-denied")
      ) {
        errorMessage =
          "Authentication was rejected. Please ensure you're using a valid Telegram account and try again.";
      }

      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isTelegramApp) {
    return (
      <div className="text-center p-4">
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 font-medium mb-2">
            ⚠️ Not in Telegram Environment
          </p>
          <p className="text-yellow-700 text-sm">
            This app is designed to work within Telegram Web App.
            <br />
            Please open it from a Telegram bot or mini app to use Telegram
            authentication.
          </p>
        </div>
        <button
          disabled
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gray-300 text-gray-500 h-10 px-4 py-2"
        >
          Sign in with Telegram (Unavailable)
        </button>
      </div>
    );
  }

  return (
    <div className="text-center p-4">
      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <p className="text-green-800 font-medium mb-1">
          ✅ Telegram Environment Detected
        </p>
        <p className="text-green-700 text-sm">
          You can now authenticate with your Telegram account.
        </p>
      </div>

      <button
        onClick={handleTelegramAuth}
        disabled={isLoading}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
      >
        {isLoading ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Signing in...
          </>
        ) : (
          "Sign in with Telegram"
        )}
      </button>

      <p className="text-xs text-gray-500 mt-3">
        Note: This requires the Firebase Functions to be properly configured
        with your Telegram bot token.
      </p>
    </div>
  );
};

export default TelegramAuth;
