import CryptoJS from "crypto-js";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface TelegramWebAppInitData {
  user?: TelegramUser;
  chat_instance?: string;
  chat_type?: string;
  auth_date: number;
  hash: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

/**
 * Validates Telegram Web App init data using the bot token
 * Based on Telegram's validation algorithm
 */
export function validateTelegramWebAppData(
  initData: string,
  botToken: string
): ValidationResult {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort((a, b) =>
      a[0].localeCompare(b[0])
    )) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

/**
 * Extracts user information from Telegram Web App
 * This should be called on the client side
 */
export function getTelegramWebAppData(): TelegramWebAppInitData | null {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    // First try to use @twa-dev/sdk if available
    let webAppData: any = null;
    let initData = "";

    // Try to dynamically import @twa-dev/sdk
    try {
      // Check if we can access the SDK
      if (typeof window !== "undefined" && (window as any).Telegram?.WebApp) {
        const webApp = (window as any).Telegram.WebApp;
        initData = webApp.initData ?? "";
        webAppData = webApp.initDataUnsafe ?? {};

        console.log("Using direct Telegram WebApp access");
        console.log("initData:", initData);
        console.log("initDataUnsafe:", webAppData);
      }
    } catch (sdkError) {
      console.warn(
        "@twa-dev/sdk not available, falling back to direct access:",
        sdkError
      );
    }

    // Fallback to direct window.Telegram access
    if (!initData && window.Telegram?.WebApp) {
      const webApp = window.Telegram.WebApp;
      initData = typeof webApp.initData === "string" ? webApp.initData : "";
      webAppData = webApp.initDataUnsafe ?? {};

      console.log("Using fallback Telegram WebApp access");
      console.log("initData:", initData);
      console.log("initDataUnsafe:", webAppData);
    }

    if (!initData) {
      console.warn("Telegram WebApp initData is empty");
      console.warn("Available Telegram object:", window.Telegram);
      return null;
    }

    // Parse init data from URL parameters
    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get("user");
    const authDate = urlParams.get("auth_date");
    const hash = urlParams.get("hash");

    console.log("Parsed URL params:", {
      userParam: userParam ? "present" : "missing",
      authDate: authDate ? "present" : "missing",
      hash: hash ? "present" : "missing",
    });

    if (!userParam || !authDate || !hash) {
      console.warn("Missing required Telegram data fields:", {
        userParam: !!userParam,
        authDate: !!authDate,
        hash: !!hash,
        rawInitData: initData,
      });
      return null;
    }

    const user: TelegramUser = JSON.parse(userParam);

    return {
      user,
      chat_instance: urlParams.get("chat_instance") ?? undefined,
      chat_type: urlParams.get("chat_type") ?? undefined,
      auth_date: parseInt(authDate, 10),
      hash,
    };
  } catch (error) {
    console.error("Error getting Telegram Web App data:", error);
    return null;
  }
}

/**
 * Initializes Telegram Web App
 * Should be called when the app loads
 */
export async function initTelegramWebApp(): Promise<boolean> {
  if (typeof window === "undefined") {
    return false;
  }

  try {
    // First try to use @twa-dev/sdk
    try {
      const WebApp = await import("@twa-dev/sdk");
      console.log("Using @twa-dev/sdk for initialization");

      WebApp.default.ready();
      WebApp.default.expand();

      // Set theme colors
      if (WebApp.default.setHeaderColor) {
        WebApp.default.setHeaderColor("#000000");
      }
      if (WebApp.default.setBackgroundColor) {
        WebApp.default.setBackgroundColor("#ffffff");
      }

      console.log("Telegram Web App initialized with @twa-dev/sdk");
      return true;
    } catch (sdkError) {
      console.warn(
        "@twa-dev/sdk not available, falling back to direct access:",
        sdkError
      );
    }

    // Fallback to direct window.Telegram access
    if (
      !window.Telegram?.WebApp ||
      typeof window.Telegram.WebApp !== "object"
    ) {
      console.warn("Telegram WebApp not available");
      return false;
    }

    const webApp = window.Telegram.WebApp;

    // Initialize the web app safely
    if (typeof webApp.ready === "function") {
      webApp.ready();
    }

    // Expand the web app to full height safely
    if (typeof webApp.expand === "function") {
      webApp.expand();
    }

    // Set theme colors safely
    if (typeof webApp.setHeaderColor === "function") {
      webApp.setHeaderColor("#000000");
    }
    if (typeof webApp.setBackgroundColor === "function") {
      webApp.setBackgroundColor("#ffffff");
    }

    console.log("Telegram Web App initialized with direct access");
    return true;
  } catch (error) {
    console.error("Error initializing Telegram Web App:", error);
    return false;
  }
}

/**
 * Checks if the app is running inside Telegram
 */
export function isTelegramWebApp(): boolean {
  if (typeof window === "undefined") {
    return false;
  }

  // Check for Telegram WebApp availability more safely
  try {
    return !!(
      window.Telegram?.WebApp && typeof window.Telegram.WebApp === "object"
    );
  } catch (error) {
    console.warn("Error checking Telegram WebApp availability:", error);
    return false;
  }
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    Telegram?: {
      WebApp: {
        ready: () => void;
        expand: () => void;
        setHeaderColor: (color: string) => void;
        setBackgroundColor: (color: string) => void;
        initData: string;
        initDataUnsafe: {
          user?: TelegramUser;
          chat_instance?: string;
          chat_type?: string;
          auth_date?: number;
          hash?: string;
        };
      };
    };
  }
}
